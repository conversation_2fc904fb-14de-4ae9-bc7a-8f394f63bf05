/* HD Digital Premium Theme */

/* CSS Variables - Premium Color Palette */
:root {
    /* Primary Colors - Warm and inviting */
    --primary-900: #0f172a;
    --primary-800: #1e293b;
    --primary-700: #334155;
    --primary-600: #475569;
    --primary-500: #64748b;

    /* Accent Colors - Vibrant and happy */
    --accent-primary: #3b82f6;    /* Bright blue */
    --accent-secondary: #10b981;  /* Emerald green */
    --accent-tertiary: #f59e0b;   /* Warm amber */
    --accent-quaternary: #ec4899; /* Pink */
    --accent-purple: #8b5cf6;     /* Purple */
    --accent-cyan: #06b6d4;       /* Cyan */
    --accent-orange: #f97316;     /* Orange */

    /* Gradient Colors - More vibrant and energetic */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #f59e0b 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ec4899 100%);
    --gradient-hero: linear-gradient(135deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);

    /* Text Colors - Improved contrast for better readability */
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #cbd5e1;
    --text-dark: #1e293b;

    /* Background Colors - Lighter and more welcoming */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: rgba(248, 250, 252, 0.08);
    --bg-glass: rgba(248, 250, 252, 0.12);

    /* Spacing - Optimized for more compact layout */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.875rem;
    --spacing-md: 1.25rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3.5rem;

    /* Layout */
    --container-max-width: 1400px;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Playfair Display', serif;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-premium: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Borders */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Premium Header */
.premium-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.premium-nav {
    padding: var(--spacing-sm) 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo .logo-link {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.logo-accent {
    color: var(--accent-primary);
    font-size: 2rem;
    margin-left: 2px;
}

.nav-logo:hover .logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Desktop Navigation */
.desktop-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    position: relative;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    padding: var(--spacing-xs) 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link:hover::before {
    width: 100%;
}



/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-content {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.mobile-nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: color var(--transition-normal);
}

.mobile-nav-link:hover {
    color: var(--text-primary);
}

.mobile-cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius-xl);
    font-weight: 600;
    margin-top: var(--spacing-md);
}

/* Hero Section - Premium Enhanced */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    background:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.08) 0%, transparent 70%);
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: gridMove 30s linear infinite;
    opacity: 0.6;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 15%, rgba(59, 130, 246, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(16, 185, 129, 0.10) 0%, transparent 45%),
        radial-gradient(circle at 45% 65%, rgba(245, 158, 11, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.06) 0%, transparent 35%);
    animation: particleFloat 20s ease-in-out infinite;
}

@keyframes particleFloat {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

.hero-container {
    max-width: none;
    margin: 0;
    padding-left: max(var(--spacing-lg), calc((100vw - var(--container-max-width)) / 2 + var(--spacing-lg)));
    padding-right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    z-index: 2;
    min-height: calc(100vh - 120px);
    text-align: left;
}

.hero-content {
    z-index: 3;
    animation: heroContentFadeIn 1.2s ease-out;
    width: 100%;
}

@keyframes heroContentFadeIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.hero-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.hero-badge:hover::before {
    left: 100%;
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-2xl);
    letter-spacing: -0.02em;
    position: relative;
}

.title-line {
    display: block;
    line-height: 0.9;
    margin: 0;
    padding: 0;
    animation: titleLineSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(-30px);
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) { animation-delay: 0.6s; }

@keyframes titleLineSlideIn {
    to { opacity: 1; transform: translateX(0); }
}

.gradient-text {
    background: var(--gradient-hero);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-hero);
    border-radius: 2px;
    opacity: 0.6;
    animation: underlineGlow 2s ease-in-out infinite;
}

@keyframes underlineGlow {
    0%, 100% { opacity: 0.6; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.05); }
}

/* HDCode Clean Animation */
.hdcode-animated {
    font-weight: 900;
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hdcodeGradientShift 4s ease-in-out infinite;
    transition: all 0.3s ease;
}

@keyframes hdcodeGradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* HDCode Hover Effect */
.hdcode-animated:hover {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */
@media (prefers-reduced-motion: reduce) {
    .hdcode-animated {
        animation: none;
        background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

.hero-description {
    font-size: 1.3rem;
    color: var(--text-primary);
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
    max-width: 580px;
    font-weight: 400;
    opacity: 0;
    animation: descriptionFadeIn 1s ease-out 0.8s forwards;
}

@keyframes descriptionFadeIn {
    to { opacity: 1; }
}

.hero-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0;
    animation: actionsFadeIn 1s ease-out 1s forwards;
}

@keyframes actionsFadeIn {
    to { opacity: 1; }
}

.primary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius-2xl);
    font-weight: 700;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
}

.secondary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    text-decoration: none;
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-2xl);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.secondary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.secondary-button:hover::before {
    opacity: 1;
}

.secondary-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}



/* Hero Visual - Premium Enhanced */
.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    animation: visualFadeIn 1.5s ease-out 0.5s forwards;
}

@keyframes visualFadeIn {
    to { opacity: 1; }
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
}

.hero-image-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(16, 185, 129, 0.15) 50%, rgba(245, 158, 11, 0.1) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: heroOrb 8s ease-in-out infinite;
    filter: blur(40px);
}

@keyframes heroOrb {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
}

.hero-image-container::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 20%;
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, rgba(168, 85, 247, 0.15) 0%, rgba(6, 182, 212, 0.1) 100%);
    border-radius: 50%;
    animation: heroOrbSecondary 6s ease-in-out infinite reverse;
    filter: blur(30px);
}

@keyframes heroOrbSecondary {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
    50% { transform: scale(1.3) rotate(180deg); opacity: 0.9; }
}

/* Premium geometric shapes */
.hero-visual::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-lg);
    animation: geometricFloat 4s ease-in-out infinite;
}

@keyframes geometricFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(45deg); }
}

.hero-visual::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 15%;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(245, 158, 11, 0.15) 100%);
    border-radius: 50%;
    animation: geometricPulse 3s ease-in-out infinite;
}

@keyframes geometricPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* Hero Code Preview */
.hero-code-preview {
    position: relative;
    z-index: 10;
    animation: codePreviewSlideIn 1.5s ease-out 1s forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes codePreviewSlideIn {
    to { opacity: 1; transform: translateY(0); }
}

.code-window {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-width: 350px;
    margin: 0 auto;
}

.code-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    opacity: 0.8;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.code-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.code-content {
    padding: var(--spacing-lg);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

.code-line {
    margin-bottom: var(--spacing-xs);
    animation: codeLineType 0.8s ease-out forwards;
    opacity: 0;
}

.code-line:nth-child(1) { animation-delay: 1.5s; }
.code-line:nth-child(2) { animation-delay: 1.8s; }
.code-line:nth-child(3) { animation-delay: 2.1s; }

@keyframes codeLineType {
    to { opacity: 1; }
}

.code-keyword { color: var(--accent-quaternary); }
.code-variable { color: var(--accent-primary); }
.code-function { color: var(--accent-secondary); }
.code-operator { color: var(--accent-tertiary); }
.code-comment { color: var(--text-muted); font-style: italic; }
/* Hero Responsive Design */
@media (max-width: 1024px) {
    .hero-title {
        font-size: clamp(2.5rem, 8vw, 4rem);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding-top: 60px;
        min-height: 85vh;
    }

    .hero-container {
        padding: 0 var(--spacing-lg);
        min-height: calc(85vh - 80px);
    }

    .hero-title {
        font-size: clamp(2rem, 10vw, 3.5rem);
        line-height: 1.1;
        margin-bottom: var(--spacing-md);
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: var(--spacing-lg);
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .primary-button,
    .secondary-button {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .hero-section {
        min-height: 80vh;
    }

    .hero-container {
        padding: 0 var(--spacing-md);
        min-height: calc(80vh - 60px);
    }

    .hero-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
        margin-bottom: var(--spacing-sm);
    }

    .hero-title {
        font-size: clamp(1.8rem, 12vw, 3rem);
        line-height: 1.1;
        margin-bottom: var(--spacing-sm);
    }

    .hero-description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: var(--spacing-md);
    }

    .hero-actions {
        margin-bottom: var(--spacing-md);
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }
}

/* Services Section */
.services-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.section-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--border-radius-xl);
    color: var(--accent-secondary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    backdrop-filter: blur(10px);
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: 1.125rem;
    color: var(--text-primary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.service-card {
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-premium);
}

.service-card.featured {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(59, 130, 246, 0.3);
}

.service-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--gradient-accent);
    color: var(--text-primary);
    font-size: 0.75rem;
    font-weight: 700;
    border-radius: var(--border-radius-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-lg);
}

.service-icon i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.service-title {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.service-description {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-md);
}

.service-features li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-weight: bold;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all var(--transition-normal);
}

.service-link:hover {
    gap: var(--spacing-sm);
    color: var(--accent-secondary);
}

.service-link i {
    transition: transform var(--transition-normal);
}

.service-link:hover i {
    transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    /* No hero stats specific styles needed */
}

@media (max-width: 768px) {
    .desktop-menu,
    .nav-cta {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu.active {
        display: block;
    }

    .services-section {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-xl);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .service-card {
        padding: var(--spacing-md);
    }
}

/* Experience Domains Section - Premium Enhanced */
.experience-section {
    position: relative;
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    overflow: hidden;
}

.experience-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.experience-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.04) 0%, transparent 50%);
    animation: experienceParticleFloat 25s ease-in-out infinite;
}

@keyframes experienceParticleFloat {
    0%, 100% { opacity: 0.6; transform: scale(1) rotate(0deg); }
    33% { opacity: 0.8; transform: scale(1.05) rotate(120deg); }
    66% { opacity: 0.7; transform: scale(0.95) rotate(240deg); }
}

.experience-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.experience-orbs::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 20s ease-in-out infinite;
    filter: blur(40px);
}

.experience-orbs::after {
    content: '';
    position: absolute;
    bottom: 20%;
    right: 15%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 15s ease-in-out infinite reverse;
    filter: blur(30px);
}

@keyframes experienceOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    50% { transform: translate(30px, -20px) scale(1.2); opacity: 0.8; }
}

.experience-grid {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.experience-card {
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-2xl);
    padding: 0;
    transition: all var(--transition-slow);
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: experienceCardFadeIn 0.8s ease-out forwards;
}

.experience-card[data-index="0"] { animation-delay: 0.1s; }
.experience-card[data-index="1"] { animation-delay: 0.2s; }
.experience-card[data-index="2"] { animation-delay: 0.3s; }
.experience-card[data-index="3"] { animation-delay: 0.4s; }
.experience-card[data-index="4"] { animation-delay: 0.5s; }
.experience-card[data-index="5"] { animation-delay: 0.6s; }
.experience-card[data-index="6"] { animation-delay: 0.7s; }
.experience-card[data-index="7"] { animation-delay: 0.8s; }
.experience-card[data-index="8"] { animation-delay: 0.9s; }

@keyframes experienceCardFadeIn {
    to { opacity: 1; transform: translateY(0); }
}

.experience-card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    transition: all var(--transition-slow);
}

.experience-card:hover .experience-card-background {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

.experience-card-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-lg);
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 180px;
}

.experience-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-slow);
    z-index: 3;
}

.experience-card:hover::before {
    transform: scaleX(1);
}

.experience-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow:
        var(--shadow-premium),
        0 0 40px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.experience-icon-container {
    position: relative;
    margin-bottom: var(--spacing-md);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.experience-icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transition: all var(--transition-slow);
    animation: experienceIconPulse 4s ease-in-out infinite;
}

@keyframes experienceIconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.experience-card:hover .experience-icon-bg {
    transform: scale(1.2);
    opacity: 1;
}

.experience-icon {
    position: relative;
    z-index: 2;
    font-size: 2.5rem;
    transition: all var(--transition-slow);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.experience-card:hover .experience-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.experience-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
    margin: 0;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    z-index: 2;
}

.experience-card:hover .experience-name {
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.experience-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-slow);
    z-index: 1;
    filter: blur(20px);
}

.experience-card:hover .experience-glow {
    opacity: 1;
}

/* Responsive Design for Experience Section */
@media (max-width: 1024px) {
    .experience-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .experience-section {
        padding: var(--spacing-xl) 0;
    }

    .experience-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .experience-card-content {
        padding: var(--spacing-md);
        min-height: 160px;
    }

    .experience-icon-container {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-sm);
    }

    .experience-icon {
        font-size: 1.8rem;
    }

    .experience-name {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .experience-card:hover {
        transform: translateY(-8px) scale(1.01);
    }
}

@media (max-width: 480px) {
    .services-section,
    .experience-section,
    .about-section,
    .contact-section {
        padding: var(--spacing-lg) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-lg);
    }

    .services-grid,
    .experience-grid {
        margin-top: var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .experience-grid {
        grid-template-columns: 1fr;
    }

    .experience-card-content {
        padding: var(--spacing-sm);
        min-height: 140px;
    }

    .experience-icon-container {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-xs);
    }

    .experience-icon {
        font-size: 1.5rem;
    }

    .experience-name {
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .service-card {
        padding: var(--spacing-sm);
    }

    .service-icon {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-sm);
    }

    .service-title {
        font-size: 1.25rem;
        margin-bottom: var(--spacing-xs);
    }

    .service-description {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-sm);
    }

    .service-features {
        margin-bottom: var(--spacing-sm);
    }
}

/* Portfolio Section */
.portfolio-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.portfolio-item {
    position: relative;
    height: 300px;
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-premium);
}

.portfolio-item:hover .portfolio-image {
    background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--accent-tertiary) 100%);
}

.portfolio-image {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.portfolio-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    color: var(--text-primary);
    text-align: center;
    z-index: 1;
    transition: all var(--transition-normal);
}

.portfolio-preview i {
    font-size: 3rem;
    opacity: 0.9;
}

.preview-text h4 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.preview-text span {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 500;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-md);
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    z-index: 2;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
    transform: translateY(0);
}

.portfolio-item:hover .portfolio-preview {
    opacity: 0;
    transform: scale(0.9);
}

.portfolio-info h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.portfolio-info p {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
    font-size: 0.95rem;
}

.portfolio-tech {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.portfolio-tech span {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
    color: var(--accent-primary);
}

.portfolio-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.portfolio-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.portfolio-link:hover {
    background: var(--gradient-primary);
    transform: scale(1.1);
}

.portfolio-cta {
    text-align: center;
}

/* About Section */
.about-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.about-description {
    font-size: 1.125rem;
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1.25rem;
}

.stat-number {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    text-align: center;
}

.tech-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
}

.tech-item i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.tech-item span {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}
/* Contact Section */
.contact-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-details h4 {
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-primary);
    margin: 0;
}

.premium-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 1rem;
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-secondary);
    opacity: 0.8;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.premium-form .primary-button {
    align-self: flex-start;
    margin-top: var(--spacing-md);
}

/* Additional responsive styles */
@media (max-width: 1024px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: var(--spacing-lg) auto;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        max-width: 400px;
        margin: 0 auto;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .about-section,
    .contact-section {
        padding: var(--spacing-xl) 0;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .contact-content {
        gap: var(--spacing-lg);
        margin-top: var(--spacing-lg);
    }
}

/* Form Message Styles */
.form-message {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.form-message.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-message.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.form-message.success i {
    color: #22c55e;
    margin-right: var(--spacing-sm);
}

.form-message.error i {
    color: #ef4444;
    margin-right: var(--spacing-sm);
}

/* Loading state for submit button */
.primary-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.primary-button.loading .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Contact Action Section */
.contact-action {
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-cta {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    max-width: 500px;
    width: 100%;
}

.contact-cta h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-cta p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.email-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    transition: all var(--transition-normal);
}

.email-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.contact-benefits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.benefit-item i {
    color: var(--accent-primary);
    font-size: 0.8rem;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .contact-cta {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-md);
    }

    .contact-benefits {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .tech-stack {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .tech-item {
        padding: var(--spacing-sm);
    }
}

/* Additional mobile optimizations for very small screens */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .section-container {
        padding: 0 var(--spacing-md);
    }

    .contact-cta {
        padding: var(--spacing-md);
        margin: 0 var(--spacing-sm);
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
        margin: var(--spacing-md) auto;
    }

    .stat-card {
        padding: var(--spacing-xs);
    }

    .contact-item {
        padding: var(--spacing-sm);
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}
